/* B<PERSON>vani Doors Admin - Global Styles */

@use '@angular/material' as mat;

// Define the theme using Angular Material v20 theming system
html {
  color-scheme: light dark;
  @include mat.theme((
    color: mat.$azure-palette,
    typography: Roboto,
    density: 0
  ));
}

// Global styles for the admin interface
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f5f5f5;
}

// Custom overrides for better admin interface appearance
html {
  @include mat.card-overrides((
    elevated-container-shape: 8px,
  ));

  @include mat.button-overrides((
    filled-container-shape: 6px,
    outlined-container-shape: 6px,
    text-container-shape: 6px,
    tonal-container-shape: 6px,
  ));

  @include mat.form-field-overrides((
    filled-container-shape: 6px,
    outlined-container-shape: 6px,
  ));
}

// Responsive classes based on BreakpointObserver
// These classes are applied dynamically by ResponsiveService

// Container responsive classes
.responsive-container {
  width: 100%;
  box-sizing: border-box;
}

.responsive-mobile .responsive-container {
  padding: 16px;
}

.responsive-tablet .responsive-container {
  padding: 24px;
}

.responsive-desktop .responsive-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
}

// Responsive grid system
.responsive-grid {
  display: grid;
  gap: 16px;
}

.responsive-mobile .responsive-grid {
  grid-template-columns: 1fr;
}

.responsive-tablet .responsive-grid {
  grid-template-columns: repeat(2, 1fr);
}

.responsive-desktop .responsive-grid {
  grid-template-columns: repeat(3, 1fr);
}

// Responsive table wrapper
.responsive-table-wrapper {
  overflow-x: auto;
}

.responsive-mobile .responsive-table-wrapper .mat-mdc-table {
  min-width: 100%;
}

.responsive-tablet .responsive-table-wrapper .mat-mdc-table,
.responsive-desktop .responsive-table-wrapper .mat-mdc-table {
  min-width: 600px;
}

// Responsive visibility utilities
.responsive-mobile .mobile-hidden {
  display: none !important;
}

.responsive-tablet .tablet-hidden,
.responsive-desktop .desktop-hidden {
  display: none !important;
}

.responsive-small .small-hidden {
  display: none !important;
}

.responsive-large .large-hidden {
  display: none !important;
}

// Admin specific styles
.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.admin-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}

.admin-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

// Table responsive styles
.mat-mdc-table {
  @media (max-width: 768px) {
    font-size: 12px;

    .mat-mdc-header-cell,
    .mat-mdc-cell {
      padding: 8px 4px;
    }
  }
}
