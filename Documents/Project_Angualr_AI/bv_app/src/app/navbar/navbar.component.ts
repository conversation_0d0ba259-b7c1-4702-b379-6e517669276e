import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';
import { Auth } from '../core/auth/auth';
import { ResponsiveService } from '../core/services/responsive.service';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss',
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    RouterModule,
  ]
})
export class NavbarComponent {
  private responsiveService = inject(ResponsiveService);
  private auth = inject(Auth);

  // Signal-based responsive detection using ResponsiveService
  protected readonly isHandset = computed(() => this.responsiveService.isHandset());
  protected readonly isMobile = computed(() => this.responsiveService.isMobile());
  protected readonly sideNavMode = computed(() => this.responsiveService.sideNavMode());
  protected readonly showSideNav = computed(() => this.responsiveService.showSideNav());

  // Auth signals
  protected readonly user = this.auth.user;
  protected readonly isAuthenticated = this.auth.isAuthenticated;

  protected readonly menuItems = [
    { icon: 'dashboard', label: 'Dashboard', route: '/dashboard' },
    { icon: 'inventory_2', label: 'Materials', route: '/materials' },
    { icon: 'local_shipping', label: 'Suppliers', route: '/suppliers' },
    { icon: 'people', label: 'Customers', route: '/customers' },
    { icon: 'shopping_cart', label: 'Orders', route: '/orders' },
    { icon: 'receipt', label: 'GST', route: '/gst' },
    { icon: 'assessment', label: 'Reports', route: '/reports' },
  ];

  logout(): void {
    this.auth.logout();
  }
}
