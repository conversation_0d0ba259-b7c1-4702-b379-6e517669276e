@if (isAuthenticated()) {
  <mat-sidenav-container class="sidenav-container">
    <mat-sidenav #drawer class="sidenav" fixedInViewport
        [attr.role]="isMobile() ? 'dialog' : 'navigation'"
        [mode]="sideNavMode()"
        [opened]="showSideNav()">

      <!-- Sidebar Header -->
      <mat-toolbar class="sidenav-header">
        <div class="brand">
          <mat-icon class="brand-icon">store</mat-icon>
          <span class="brand-text">Bhavani Doors</span>
        </div>
      </mat-toolbar>

      <!-- Navigation Menu -->
      <mat-nav-list class="nav-list">
        @for (item of menuItems; track item.route) {
          <a mat-list-item [routerLink]="item.route" routerLinkActive="active-link">
            <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
            <span matListItemTitle>{{ item.label }}</span>
          </a>
        }

        <mat-divider></mat-divider>

        <!-- User Profile Section -->
        <div class="user-section">
          <mat-list-item class="user-info">
            <mat-icon matListItemIcon>account_circle</mat-icon>
            <div matListItemTitle class="user-details">
              <span class="user-name">{{ user()?.first_name || user()?.username }}</span>
              <span class="user-role">{{ user()?.is_superuser ? 'Super Admin' : 'Admin' }}</span>
            </div>
          </mat-list-item>

          <mat-list-item (click)="logout()" class="logout-item">
            <mat-icon matListItemIcon>logout</mat-icon>
            <span matListItemTitle>Logout</span>
          </mat-list-item>
        </div>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content>
      <!-- Top Toolbar -->
      <mat-toolbar color="primary" class="top-toolbar">
        @if (isMobile()) {
          <button
            type="button"
            aria-label="Toggle sidenav"
            mat-icon-button
            (click)="drawer.toggle()">
            <mat-icon>menu</mat-icon>
          </button>
        }

        <span class="toolbar-title">Bhavani Doors Admin</span>

        <span class="toolbar-spacer"></span>

        <!-- User Menu for Desktop -->
        @if (!isMobile()) {
          <button mat-icon-button [matMenuTriggerFor]="userMenu">
            <mat-icon>account_circle</mat-icon>
          </button>
          <mat-menu #userMenu="matMenu">
            <div class="user-menu-header">
              <span class="user-name">{{ user()?.first_name || user()?.username }}</span>
              <span class="user-email">{{ user()?.email }}</span>
            </div>
            <mat-divider></mat-divider>
            <button mat-menu-item (click)="logout()">
              <mat-icon>logout</mat-icon>
              <span>Logout</span>
            </button>
          </mat-menu>
        }
      </mat-toolbar>

      <!-- Main Content Area -->
      <div class="main-content">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
} @else {
  <!-- Show content without navigation for non-authenticated users -->
  <router-outlet></router-outlet>
}
