<!-- <PERSON><PERSON><PERSON> Doors Inventory Management System -->
<!-- Main Application Shell with Angular Material Design -->

<div class="app-container" [class.offline]="!isOnline()" [class]="containerClasses()">
  <!-- Loading Spinner for Initial App Load -->
  @if (isLoading()) {
    <app-loading
      message="Loading Bhavani Doors Admin"
      submessage="Preparing your inventory management system"
      [showIcon]="true"
      icon="store"
      [compact]="isMobile()">
    </app-loading>
  } @else {
    <!-- Main Application Content -->
    <app-navbar></app-navbar>
  }
</div>

<!-- Offline Indicator -->
@if (!isOnline()) {
  <div class="offline-banner" role="alert" aria-live="polite">
    <mat-icon>wifi_off</mat-icon>
    <span>You are currently offline. Some features may not be available.</span>
    <button mat-button class="retry-button" (click)="checkConnection()" aria-label="Retry connection">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>
}

<!-- Global Error Handler -->
@if (globalError()) {
  <div class="error-banner" role="alert" aria-live="assertive">
    <mat-icon>error</mat-icon>
    <span>{{ globalError() }}</span>
    <button mat-button (click)="dismissError()" aria-label="Dismiss error">
      <mat-icon>close</mat-icon>
    </button>
  </div>
}

<!-- Accessibility Skip Links -->
<div class="skip-links">
  <a class="skip-link" href="#main-content">Skip to main content</a>
  <a class="skip-link" href="#navigation">Skip to navigation</a>
</div>