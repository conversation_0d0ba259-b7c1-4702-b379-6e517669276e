import { Component, input, output, computed, inject, TemplateRef, ContentChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { LoadingComponent } from '../loading/loading.component';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  mobileHidden?: boolean;
  tabletHidden?: boolean;
}

/**
 * Responsive Data Table Component
 * Automatically adapts table layout based on screen size using BreakpointObserver
 * Provides consistent table behavior across the application
 */
@Component({
  selector: 'app-responsive-table',
  imports: [
    CommonModule,
    MatTableModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatPaginatorModule,
    MatSortModule,
    LoadingComponent
  ],
  template: `
    <mat-card class="table-card">
      @if (title()) {
        <mat-card-header>
          <mat-card-title>{{ title() }}</mat-card-title>
          @if (subtitle()) {
            <mat-card-subtitle>{{ subtitle() }}</mat-card-subtitle>
          }
        </mat-card-header>
      }

      <mat-card-content>
        @if (loading()) {
          <app-loading 
            [message]="loadingMessage()"
            [compact]="true"
            [fullHeight]="false">
          </app-loading>
        } @else if (data().length === 0) {
          <div class="no-data">
            <mat-icon>{{ emptyIcon() }}</mat-icon>
            <h3>{{ emptyTitle() }}</h3>
            <p>{{ emptyMessage() }}</p>
            @if (showAddButton()) {
              <button mat-raised-button color="primary" (click)="addClicked.emit()">
                <mat-icon>add</mat-icon>
                {{ addButtonText() }}
              </button>
            }
          </div>
        } @else {
          @if (isMobile()) {
            <!-- Mobile Card Layout -->
            <div class="mobile-cards">
              @for (item of data(); track trackByFn()(item)) {
                <mat-card class="mobile-card" (click)="rowClicked.emit(item)">
                  <ng-container 
                    [ngTemplateOutlet]="mobileCardTemplate || defaultMobileCard"
                    [ngTemplateOutletContext]="{ $implicit: item, index: $index }">
                  </ng-container>
                </mat-card>
              }
            </div>
          } @else {
            <!-- Desktop/Tablet Table Layout -->
            <div class="table-container">
              <table mat-table [dataSource]="data()" class="responsive-table" matSort>
                @for (column of visibleColumns(); track column.key) {
                  <ng-container [matColumnDef]="column.key">
                    @if (column.sortable) {
                      <th mat-header-cell *matHeaderCellDef [mat-sort-header]="column.key">
                        {{ column.label }}
                      </th>
                    } @else {
                      <th mat-header-cell *matHeaderCellDef>
                        {{ column.label }}
                      </th>
                    }
                    <td mat-cell *matCellDef="let element; let i = index">
                      <ng-container 
                        [ngTemplateOutlet]="getCellTemplate(column.key)"
                        [ngTemplateOutletContext]="{ $implicit: element, column: column, index: i }">
                      </ng-container>
                    </td>
                  </ng-container>
                }

                <tr mat-header-row *matHeaderRowDef="displayedColumns()"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns();" 
                    (click)="rowClicked.emit(row)"
                    [class.clickable]="rowClickable()">
                </tr>
              </table>
            </div>
          }

          @if (showPaginator()) {
            <mat-paginator 
              [length]="totalCount()"
              [pageSize]="pageSize()"
              [pageSizeOptions]="pageSizeOptions()"
              (page)="pageChanged.emit($event)">
            </mat-paginator>
          }
        }
      </mat-card-content>
    </mat-card>

    <!-- Default Mobile Card Template -->
    <ng-template #defaultMobileCard let-item let-index="index">
      <mat-card-content>
        @for (column of visibleColumns(); track column.key) {
          <div class="mobile-field">
            <span class="field-label">{{ column.label }}:</span>
            <span class="field-value">
              <ng-container 
                [ngTemplateOutlet]="getCellTemplate(column.key)"
                [ngTemplateOutletContext]="{ $implicit: item, column: column, index: index }">
              </ng-container>
            </span>
          </div>
        }
      </mat-card-content>
    </ng-template>
  `,
  styles: [`
    .table-card {
      margin-bottom: 24px;
    }

    .table-container {
      overflow-x: auto;
      margin: 16px 0;
    }

    .responsive-table {
      width: 100%;
      min-width: 600px;
    }

    .responsive-table tr.clickable {
      cursor: pointer;
    }

    .responsive-table tr.clickable:hover {
      background-color: var(--mat-sys-surface-variant);
    }

    .mobile-cards {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin: 16px 0;
    }

    .mobile-card {
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .mobile-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .mobile-field {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      border-bottom: 1px solid var(--mat-sys-outline-variant);
    }

    .mobile-field:last-child {
      border-bottom: none;
    }

    .field-label {
      font-weight: 500;
      color: var(--mat-sys-on-surface-variant);
      flex: 0 0 auto;
      margin-right: 16px;
    }

    .field-value {
      flex: 1;
      text-align: right;
      color: var(--mat-sys-on-surface);
    }

    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 24px;
      text-align: center;
    }

    .no-data mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: var(--mat-sys-outline);
      margin-bottom: 16px;
    }

    .no-data h3 {
      margin: 0 0 8px 0;
      color: var(--mat-sys-on-surface-variant);
      font-weight: 500;
    }

    .no-data p {
      margin: 0 0 24px 0;
      color: var(--mat-sys-on-surface-variant);
      font-size: 14px;
    }
  `]
})
export class ResponsiveTableComponent<T = any> {
  private responsiveService = inject(ResponsiveService);

  // Input properties
  readonly data = input.required<T[]>();
  readonly columns = input.required<TableColumn[]>();
  readonly title = input<string>('');
  readonly subtitle = input<string>('');
  readonly loading = input<boolean>(false);
  readonly loadingMessage = input<string>('Loading data...');
  readonly emptyTitle = input<string>('No data available');
  readonly emptyMessage = input<string>('There are no items to display.');
  readonly emptyIcon = input<string>('inbox');
  readonly showAddButton = input<boolean>(false);
  readonly addButtonText = input<string>('Add New');
  readonly rowClickable = input<boolean>(true);
  readonly showPaginator = input<boolean>(false);
  readonly totalCount = input<number>(0);
  readonly pageSize = input<number>(10);
  readonly pageSizeOptions = input<number[]>([5, 10, 25, 50]);
  readonly trackByFn = input<(item: T) => any>((item: any) => item.id || item);

  // Output events
  readonly rowClicked = output<T>();
  readonly addClicked = output<void>();
  readonly pageChanged = output<any>();

  // Content projection for custom templates
  @ContentChild('mobileCardTemplate') mobileCardTemplate?: TemplateRef<any>;
  @ContentChild('cellTemplate') cellTemplate?: TemplateRef<any>;

  // Responsive computed properties
  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly isTablet = computed(() => this.responsiveService.isTablet());

  // Computed visible columns based on screen size
  readonly visibleColumns = computed(() => {
    const columns = this.columns();
    const isMobile = this.isMobile();
    const isTablet = this.isTablet();

    return columns.filter(column => {
      if (isMobile && column.mobileHidden) return false;
      if (isTablet && column.tabletHidden) return false;
      return true;
    });
  });

  readonly displayedColumns = computed(() => 
    this.visibleColumns().map(column => column.key)
  );

  getCellTemplate(columnKey: string): TemplateRef<any> | null {
    // Return custom cell template if provided
    return this.cellTemplate || null;
  }
}
