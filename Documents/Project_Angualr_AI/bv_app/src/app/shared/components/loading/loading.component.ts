import { Component, input, computed } from '@angular/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { ResponsiveContainerComponent } from '../responsive-container/responsive-container.component';

/**
 * Reusable Loading Component
 * Provides consistent loading states across the application
 * Responsive design using ResponsiveContainerComponent
 */
@Component({
  selector: 'app-loading',
  imports: [
    MatProgressSpinnerModule,
    MatIconModule,
    ResponsiveContainerComponent
  ],
  template: `
    <app-responsive-container 
      layout="flex" 
      alignment="center" 
      [fullHeight]="fullHeight()">
      <div class="loading-content" [class.compact]="compact()">
        @if (showIcon()) {
          <mat-icon class="loading-icon">{{ icon() }}</mat-icon>
        }
        
        <mat-spinner 
          [diameter]="spinnerSize()"
          [strokeWidth]="strokeWidth()">
        </mat-spinner>
        
        @if (message()) {
          <p class="loading-message">{{ message() }}</p>
        }
        
        @if (submessage()) {
          <p class="loading-submessage">{{ submessage() }}</p>
        }
      </div>
    </app-responsive-container>
  `,
  styles: [`
    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 48px 24px;
      max-width: 400px;
      width: 100%;
    }

    .loading-content.compact {
      padding: 24px 16px;
      max-width: 300px;
    }

    .loading-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: var(--mat-sys-primary);
      margin-bottom: 24px;
    }

    .loading-content.compact .loading-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      margin-bottom: 16px;
    }

    mat-spinner {
      margin-bottom: 24px;
    }

    .loading-content.compact mat-spinner {
      margin-bottom: 16px;
    }

    .loading-message {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
      color: var(--mat-sys-on-surface);
    }

    .loading-content.compact .loading-message {
      font-size: 16px;
    }

    .loading-submessage {
      margin: 0;
      font-size: 14px;
      color: var(--mat-sys-on-surface-variant);
      opacity: 0.8;
    }

    .loading-content.compact .loading-submessage {
      font-size: 13px;
    }

    /* Responsive adjustments */
    @container (max-width: 480px) {
      .loading-content {
        padding: 32px 16px;
      }

      .loading-icon {
        font-size: 40px;
        width: 40px;
        height: 40px;
      }

      .loading-message {
        font-size: 16px;
      }

      .loading-submessage {
        font-size: 13px;
      }
    }
  `]
})
export class LoadingComponent {
  // Input properties
  readonly message = input<string>('Loading...');
  readonly submessage = input<string>('');
  readonly icon = input<string>('');
  readonly showIcon = input<boolean>(false);
  readonly compact = input<boolean>(false);
  readonly fullHeight = input<boolean>(true);
  readonly size = input<'small' | 'medium' | 'large'>('medium');

  // Computed properties for responsive sizing
  readonly spinnerSize = computed(() => {
    const size = this.size();
    const compact = this.compact();
    
    if (compact) {
      return size === 'small' ? 24 : size === 'medium' ? 32 : 40;
    }
    
    return size === 'small' ? 32 : size === 'medium' ? 48 : 64;
  });

  readonly strokeWidth = computed(() => {
    const size = this.size();
    return size === 'small' ? 3 : size === 'medium' ? 4 : 5;
  });
}
