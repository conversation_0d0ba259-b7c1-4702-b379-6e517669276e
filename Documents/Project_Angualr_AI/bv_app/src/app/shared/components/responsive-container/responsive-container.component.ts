import { Component, inject, computed, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResponsiveService } from '../../../core/services/responsive.service';

/**
 * Responsive Container Component
 * Provides consistent responsive behavior across the application
 * Uses BreakpointObserver instead of CSS media queries
 */
@Component({
  selector: 'app-responsive-container',
  imports: [CommonModule],
  template: `
    <div 
      class="responsive-container"
      [class]="containerClasses()"
      [style.padding]="containerPadding()"
      [style.gap]="containerGap()">
      <ng-content></ng-content>
    </div>
  `,
  styles: [`
    .responsive-container {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .responsive-container.grid {
      display: grid;
      grid-template-columns: repeat(var(--grid-columns, 1), 1fr);
    }

    .responsive-container.flex-row {
      flex-direction: row;
      flex-wrap: wrap;
    }

    .responsive-container.center {
      align-items: center;
      justify-content: center;
    }

    .responsive-container.space-between {
      justify-content: space-between;
    }

    .responsive-container.full-height {
      min-height: 100vh;
    }

    .responsive-container.card-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Responsive-specific styles */
    .responsive-mobile {
      --grid-columns: 1;
    }

    .responsive-tablet {
      --grid-columns: 2;
    }

    .responsive-desktop {
      --grid-columns: 3;
      max-width: 1200px;
      margin: 0 auto;
    }

    .responsive-small .flex-row {
      flex-direction: column;
    }

    .responsive-small .space-between {
      justify-content: flex-start;
      gap: 16px;
    }
  `]
})
export class ResponsiveContainerComponent {
  private responsiveService = inject(ResponsiveService);

  // Input properties for customization
  readonly layout = input<'flex' | 'grid' | 'flex-row'>('flex');
  readonly alignment = input<'start' | 'center' | 'space-between'>('start');
  readonly fullHeight = input<boolean>(false);
  readonly isCard = input<boolean>(false);
  readonly customClasses = input<string>('');

  // Computed container classes
  readonly containerClasses = computed(() => {
    const baseClasses = this.responsiveService.containerClasses();
    const layoutClass = this.layout();
    const alignmentClass = this.alignment();
    const customClasses = this.customClasses();

    return {
      ...baseClasses,
      [layoutClass]: true,
      [alignmentClass]: this.alignment() !== 'start',
      'full-height': this.fullHeight(),
      'card-container': this.isCard(),
      [customClasses]: !!customClasses
    };
  });

  // Computed padding based on screen size
  readonly containerPadding = computed(() => this.responsiveService.containerPadding());

  // Computed gap based on screen size
  readonly containerGap = computed(() => this.responsiveService.cardGap());
}
