<app-responsive-container [class]="containerClasses()" [isCard]="false" [fullHeight]="true">
  <!-- Header -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
    <h1>{{ isEditMode() ? 'Edit Order' : 'Create New Order' }}</h1>
    <button mat-icon-button (click)="onCancel()" title="Close">
      <mat-icon>close</mat-icon>
    </button>
  </app-responsive-container>

  <!-- Loading State -->
  @if (isLoading()) {
    <app-loading
      message="Loading order data..."
      [compact]="isMobile()"
      [fullHeight]="false">
    </app-loading>
  } @else {
    <!-- Order Form -->
    <form (ngSubmit)="onSubmit()">
      <!-- Basic Order Information -->
      <app-responsive-container [isCard]="true">
        <h2>Order Information</h2>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Customer</mat-label>
            <mat-select [value]="customerId()" (selectionChange)="onCustomerChange($event.value)" required>
              @for (customer of customers(); track customer.id) {
                <mat-option [value]="customer.id">
                  {{ customer.name }} - {{ customer.phone }}
                </mat-option>
              }
            </mat-select>
            @if (!customerId()) {
              <mat-error>Customer is required</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Status</mat-label>
            <mat-select [value]="status()" (selectionChange)="onStatusChange($event.value)">
              @for (option of statusOptions; track option.value) {
                <mat-option [value]="option.value">{{ option.label }}</mat-option>
              }
            </mat-select>
          </mat-form-field>
        </div>
      </app-responsive-container>

      <!-- Order Items -->
      <app-responsive-container [isCard]="true">
        <div class="section-header">
          <h2>Order Items</h2>
          <button mat-raised-button color="primary" type="button" (click)="addOrderItem()">
            <mat-icon>add</mat-icon>
            Add Item
          </button>
        </div>

        @if (orderItems().length === 0) {
          <div class="empty-state">
            <mat-icon class="empty-icon">shopping_cart</mat-icon>
            <h3>No items added</h3>
            <p>Add items to this order</p>
            <button mat-raised-button color="primary" (click)="addOrderItem()">
              <mat-icon>add</mat-icon>
              Add First Item
            </button>
          </div>
        } @else {
          <div class="items-table">
            <table mat-table [dataSource]="orderItems()" class="order-items-table">
              <!-- Material Column -->
              <ng-container matColumnDef="material">
                <th mat-header-cell *matHeaderCellDef>Material</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline">
                    <mat-select [value]="item.material_id" (selectionChange)="updateOrderItem(i, 'material_id', $event.value)">
                      @for (material of materials(); track material.id) {
                        <mat-option [value]="material.id">
                          {{ material.name }} ({{ material.design.name }})
                        </mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline">
                    <input matInput type="number" min="1"
                           [value]="item.quantity"
                           (input)="updateOrderItem(i, 'quantity', +$any($event.target).value)">
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Unit Price Column -->
              <ng-container matColumnDef="unit_price">
                <th mat-header-cell *matHeaderCellDef>Unit Price</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <mat-form-field appearance="outline">
                    <input matInput type="number" min="0" step="0.01"
                           [value]="item.unit_price"
                           (input)="updateOrderItem(i, 'unit_price', +$any($event.target).value)">
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Total Price Column -->
              <ng-container matColumnDef="total_price">
                <th mat-header-cell *matHeaderCellDef>Total Price</th>
                <td mat-cell *matCellDef="let item">
                  <strong>{{ formatCurrency(item.total_price) }}</strong>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let item; let i = index">
                  <button mat-icon-button (click)="removeOrderItem(i)" color="warn" title="Remove">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="itemColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: itemColumns;"></tr>
            </table>
          </div>

          <!-- Order Total -->
          <div class="order-total">
            <h3>Total Amount: {{ formatCurrency(totalAmount()) }}</h3>
          </div>
        }
      </app-responsive-container>

      <!-- Form Actions -->
      <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
        <button mat-button type="button" (click)="onCancel()">Cancel</button>
        <button mat-raised-button color="primary" type="submit"
                [disabled]="!isFormValid() || isSaving()">
          @if (isSaving()) {
            <mat-spinner diameter="20"></mat-spinner>
            <span>{{ isEditMode() ? 'Updating...' : 'Creating...' }}</span>
          } @else {
            <span>{{ isEditMode() ? 'Update Order' : 'Create Order' }}</span>
          }
        </button>
      </app-responsive-container>
    </form>
  }
</app-responsive-container>
