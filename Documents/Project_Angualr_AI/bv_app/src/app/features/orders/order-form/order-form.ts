import { Component, signal, computed, inject, input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { FormsModule } from '@angular/forms';
import { OrderService, Order, OrderCreateRequest } from '../../../core/services/order.service';
import { CustomerService, Customer } from '../../../core/services/customer';
import { MaterialService, Material } from '../../../core/services/material';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../../shared/components/responsive-container/responsive-container.component';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

interface OrderItemForm {
  material_id: number;
  unit_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
}

@Component({
  selector: 'app-order-form',
  imports: [
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTableModule,
    MatSnackBarModule,
    FormsModule,
    ResponsiveContainerComponent,
    LoadingComponent
  ],
  templateUrl: './order-form.html',
  styleUrl: './order-form.scss'
})
export class OrderForm implements OnInit {
  private orderService = inject(OrderService);
  private customerService = inject(CustomerService);
  private materialService = inject(MaterialService);
  private responsiveService = inject(ResponsiveService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  // Route parameter input
  readonly id = input<string>();

  // Form signals
  private customerIdSignal = signal<number | null>(null);
  private statusSignal = signal<'open' | 'closed' | 'cancelled'>('open');
  private orderItemsSignal = signal<OrderItemForm[]>([]);
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);

  // Data signals
  private customersSignal = signal<Customer[]>([]);
  private materialsSignal = signal<Material[]>([]);
  private orderSignal = signal<Order | null>(null);

  // Public readonly signals
  readonly customerId = this.customerIdSignal.asReadonly();
  readonly status = this.statusSignal.asReadonly();
  readonly orderItems = this.orderItemsSignal.asReadonly();
  readonly isLoading = this.loadingSignal.asReadonly();
  readonly isSaving = this.savingSignal.asReadonly();
  readonly customers = this.customersSignal.asReadonly();
  readonly materials = this.materialsSignal.asReadonly();
  readonly order = this.orderSignal.asReadonly();

  // Computed properties
  readonly isEditMode = computed(() => {
    const idParam = this.id();
    return idParam !== undefined && idParam !== 'new';
  });

  readonly orderId = computed(() => {
    const idParam = this.id();
    return idParam && idParam !== 'new' ? parseInt(idParam, 10) : null;
  });

  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly containerClasses = computed(() => this.responsiveService.containerClasses());

  readonly totalAmount = computed(() => {
    return this.orderItems().reduce((total, item) => total + item.total_price, 0);
  });

  readonly isFormValid = computed(() => {
    return this.customerId() !== null && this.orderItems().length > 0;
  });

  // Table columns for order items
  readonly itemColumns = ['material', 'quantity', 'unit_price', 'total_price', 'actions'];

  readonly statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'closed', label: 'Closed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  ngOnInit(): void {
    this.loadData();
    if (this.isEditMode()) {
      this.loadOrder();
    }
  }

  loadData(): void {
    this.loadingSignal.set(true);

    Promise.all([
      this.customerService.getCustomers().toPromise(),
      this.materialService.getMaterials().toPromise()
    ]).then(([customers, materials]) => {
      this.customersSignal.set(customers || []);
      this.materialsSignal.set(materials || []);
      this.loadingSignal.set(false);
    }).catch(error => {
      console.error('Error loading data:', error);
      this.loadingSignal.set(false);
    });
  }

  loadOrder(): void {
    const id = this.orderId();
    if (id) {
      this.orderService.getOrder(id).subscribe({
        next: (order) => {
          this.orderSignal.set(order);
          this.customerIdSignal.set(order.customer.id);
          this.statusSignal.set(order.status);
          // Load order items
          this.loadOrderItems(order.order_id);
        },
        error: (error) => {
          console.error('Error loading order:', error);
          this.snackBar.open('Error loading order', 'Close', { duration: 3000 });
        }
      });
    }
  }

  loadOrderItems(orderId: string): void {
    this.orderService.getOrderItems(orderId).subscribe({
      next: (items) => {
        const formItems: OrderItemForm[] = items.map(item => ({
          material_id: item.material.id,
          unit_id: item.unit.id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price
        }));
        this.orderItemsSignal.set(formItems);
      },
      error: (error) => {
        console.error('Error loading order items:', error);
      }
    });
  }

  onCustomerChange(customerId: number): void {
    this.customerIdSignal.set(customerId);
  }

  onStatusChange(status: 'open' | 'closed' | 'cancelled'): void {
    this.statusSignal.set(status);
  }

  addOrderItem(): void {
    const newItem: OrderItemForm = {
      material_id: 0,
      unit_id: 0,
      quantity: 1,
      unit_price: 0,
      total_price: 0
    };
    this.orderItemsSignal.update(items => [...items, newItem]);
  }

  removeOrderItem(index: number): void {
    this.orderItemsSignal.update(items => items.filter((_, i) => i !== index));
  }

  updateOrderItem(index: number, field: keyof OrderItemForm, value: any): void {
    this.orderItemsSignal.update(items => {
      const updatedItems = [...items];
      updatedItems[index] = { ...updatedItems[index], [field]: value };

      // Recalculate total price
      if (field === 'quantity' || field === 'unit_price') {
        updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price;
      }

      return updatedItems;
    });
  }

  getMaterialName(materialId: number): string {
    const material = this.materials().find(m => m.id === materialId);
    return material ? `${material.name} (${material.design.name})` : '';
  }

  onSubmit(): void {
    if (!this.isFormValid() || this.isSaving()) {
      return;
    }

    this.savingSignal.set(true);

    const orderData: OrderCreateRequest = {
      customer_id: this.customerId()!,
      status: this.status(),
      items: this.orderItems().map(item => ({
        material_id: item.material_id,
        unit_id: item.unit_id,
        quantity: item.quantity,
        unit_price: item.unit_price
      }))
    };

    const operation = this.isEditMode()
      ? this.orderService.updateOrder(this.orderId()!, orderData)
      : this.orderService.createOrder(orderData);

    operation.subscribe({
      next: () => {
        this.savingSignal.set(false);
        this.snackBar.open(
          this.isEditMode() ? 'Order updated successfully' : 'Order created successfully',
          'Close',
          { duration: 3000 }
        );
        this.router.navigate(['/orders']);
      },
      error: (error) => {
        this.savingSignal.set(false);
        console.error('Error saving order:', error);
        this.snackBar.open('Error saving order', 'Close', { duration: 3000 });
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/orders']);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }
}
