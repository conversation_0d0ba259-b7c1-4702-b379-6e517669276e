<div class="material-form-container">
  <!-- Header -->
  <div class="page-header">
    <h1>{{ title() }}</h1>
    <button mat-icon-button (click)="onCancel()" title="Back to Materials">
      <mat-icon>arrow_back</mat-icon>
    </button>
  </div>

  @if (isLoading()) {
    <div class="loading-container">
      <mat-spinner></mat-spinner>
      <p>Loading form data...</p>
    </div>
  } @else {
    <!-- Material Form -->
    <mat-card class="form-card">
      <mat-card-content>
        <form (ngSubmit)="onSubmit()" class="material-form">
          <div class="form-row">
            <!-- Material Name -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Material Name</mat-label>
              <input matInput
                     [value]="name()"
                     (input)="onNameChange($any($event.target).value)"
                     required>
              @if (!name().trim()) {
                <mat-error>Material name is required</mat-error>
              }
              @if (name().length > 30) {
                <mat-error>Material name cannot exceed 30 characters</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Sales Type -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Sales Type</mat-label>
              <mat-select [value]="salesType()"
                          (selectionChange)="onSalesTypeChange($event.value)"
                          required>
                <mat-option value="single">Single</mat-option>
                <mat-option value="group">Group</mat-option>
              </mat-select>
              @if (!salesType()) {
                <mat-error>Sales type is required</mat-error>
              }
            </mat-form-field>

            <!-- Design -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Design</mat-label>
              <mat-select [value]="designId()"
                          (selectionChange)="onDesignChange($event.value)"
                          required>
                @for (design of designs(); track design.id) {
                  <mat-option [value]="design.id">
                    {{ design.name }}{{ design.color && design.color !== 'NA' ? ' - ' + design.color : '' }}
                  </mat-option>
                }
              </mat-select>
              @if (!designId()) {
                <mat-error>Design is required</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Material Type -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Material Type</mat-label>
              <mat-select [value]="typeId()"
                          (selectionChange)="onTypeChange($event.value)"
                          required>
                @for (type of materialTypes(); track type.id) {
                  <mat-option [value]="type.id">{{ type.name }}</mat-option>
                }
              </mat-select>
              @if (!typeId()) {
                <mat-error>Material type is required</mat-error>
              }
            </mat-form-field>

            <!-- Unit -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Unit</mat-label>
              <mat-select [value]="unitId()"
                          (selectionChange)="onUnitChange($event.value)"
                          required>
                @for (unit of units(); track unit.id) {
                  <mat-option [value]="unit.id">{{ unit.name }}</mat-option>
                }
              </mat-select>
              @if (!unitId()) {
                <mat-error>Unit is required</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Unit Order Price -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Unit Order Price</mat-label>
              <input matInput
                     type="number"
                     [value]="unitOrderPrice()"
                     (input)="onUnitOrderPriceChange(+$any($event.target).value)"
                     required
                     min="0.01"
                     step="0.01">
              <span matPrefix>₹&nbsp;</span>
              @if (unitOrderPrice() <= 0) {
                <mat-error>Unit order price must be greater than 0</mat-error>
              }
            </mat-form-field>

            <!-- Opening Stock Quantity -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Opening Stock Quantity</mat-label>
              <input matInput
                     type="number"
                     [value]="openingStockQuantity()"
                     (input)="onOpeningStockQuantityChange(+$any($event.target).value)"
                     required
                     min="0"
                     step="0.01">
              @if (openingStockQuantity() < 0) {
                <mat-error>Opening stock quantity cannot be negative</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Opening Stock Unit Price -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Opening Stock Unit Price</mat-label>
              <input matInput
                     type="number"
                     [value]="openingStockUnitPrice()"
                     (input)="onOpeningStockUnitPriceChange(+$any($event.target).value)"
                     required
                     min="0"
                     step="0.01">
              <span matPrefix>₹&nbsp;</span>
              @if (openingStockUnitPrice() < 0) {
                <mat-error>Opening stock unit price cannot be negative</mat-error>
              }
            </mat-form-field>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <button mat-button type="button" (click)="onCancel()">
              Cancel
            </button>
            <button mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="!isFormValid() || isSaving()">
              @if (isSaving()) {
                <mat-spinner diameter="20"></mat-spinner>
                <span>{{ isEditMode() ? 'Updating...' : 'Creating...' }}</span>
              } @else {
                <span>{{ isEditMode() ? 'Update Material' : 'Create Material' }}</span>
              }
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  }
</div>
