import { Component, signal, computed, inject, input, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { httpResource } from '@angular/common/http';
import { Material, Design, Unit, MaterialType, MaterialCreateRequest } from '../../../core/services/material';

@Component({
  selector: 'app-material-form',
  templateUrl: './material-form.html',
  styleUrl: './material-form.scss',
  imports: [
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ]
})
export class MaterialForm {
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  // Route parameter input
  readonly id = input<string>();

  // Signal-based form state
  protected readonly name = signal('');
  protected readonly salesType = signal<'single' | 'group'>('single');
  protected readonly designId = signal<number | null>(null);
  protected readonly typeId = signal<number | null>(null);
  protected readonly unitId = signal<number | null>(null);
  protected readonly unitOrderPrice = signal<number>(0);
  protected readonly openingStockQuantity = signal<number>(0);
  protected readonly openingStockUnitPrice = signal<number>(0);

  // Computed edit mode
  protected readonly isEditMode = computed(() => {
    const idParam = this.id();
    return idParam !== undefined && idParam !== 'new';
  });

  protected readonly materialId = computed(() => {
    const idParam = this.id();
    return idParam && idParam !== 'new' ? parseInt(idParam, 10) : null;
  });

  // Resources for form data
  private designsResource = httpResource<Design[]>(() => '/api/designs/');
  private unitsResource = httpResource<Unit[]>(() => '/api/units/');
  private materialTypesResource = httpResource<MaterialType[]>(() => '/api/material-types/');

  private materialResource = httpResource<Material>(() => {
    const id = this.materialId();
    return id ? `/api/materials/${id}/` : undefined;
  });

  // Public readonly signals
  protected readonly isLoading = computed(() =>
    this.designsResource.isLoading() ||
    this.unitsResource.isLoading() ||
    this.materialTypesResource.isLoading() ||
    (this.isEditMode() && this.materialResource.isLoading())
  );

  protected readonly designs = computed(() => this.designsResource.value() || []);
  protected readonly units = computed(() => this.unitsResource.value() || []);
  protected readonly materialTypes = computed(() => this.materialTypesResource.value() || []);

  // Form validation
  protected readonly isFormValid = computed(() => {
    return this.name().trim().length > 0 &&
           this.designId() !== null &&
           this.typeId() !== null &&
           this.unitId() !== null &&
           this.unitOrderPrice() > 0;
  });

  protected readonly title = computed(() =>
    this.isEditMode() ? 'Edit Material' : 'Add New Material'
  );



  // Signal for save operation
  private saveRequestSignal = signal<MaterialCreateRequest | null>(null);
  private saveResource = httpResource<Material>(() => {
    const request = this.saveRequestSignal();
    if (!request) return undefined;

    const id = this.materialId();
    return {
      url: id ? `/api/materials/${id}/` : '/api/materials/',
      method: id ? 'PUT' : 'POST',
      body: request
    };
  });

  protected readonly isSaving = this.saveResource.isLoading;

  constructor() {
    // Effect to populate form when material is loaded for editing
    effect(() => {
      const material = this.materialResource.value();
      if (material) {
        this.name.set(material.name);
        this.salesType.set(material.sales_type);
        this.designId.set(material.design.id);
        this.typeId.set(material.type.id);
        this.unitId.set(material.unit.id);
        this.unitOrderPrice.set(material.unit_order_price);
        this.openingStockQuantity.set(material.opening_stock_quantity);
        this.openingStockUnitPrice.set(material.opening_stock_unit_price);
      }
    });

    // Effect to handle save success
    effect(() => {
      if (this.saveResource.hasValue()) {
        const message = this.isEditMode() ? 'Material updated successfully' : 'Material created successfully';
        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.router.navigate(['/materials']);
        this.saveRequestSignal.set(null); // Reset
      }
    });

    // Effect to handle save error
    effect(() => {
      const error = this.saveResource.error();
      if (error) {
        console.error('Error saving material:', error);
        this.snackBar.open('Error saving material', 'Close', { duration: 3000 });
        this.saveRequestSignal.set(null); // Reset
      }
    });
  }

  onSubmit(): void {
    if (this.isFormValid()) {
      const formData: MaterialCreateRequest = {
        name: this.name(),
        sales_type: this.salesType(),
        design_id: this.designId()!,
        type_id: this.typeId()!,
        unit_id: this.unitId()!,
        unit_order_price: this.unitOrderPrice(),
        opening_stock_quantity: this.openingStockQuantity(),
        opening_stock_unit_price: this.openingStockUnitPrice()
      };

      this.saveRequestSignal.set(formData);
    }
  }

  onCancel(): void {
    this.router.navigate(['/materials']);
  }

  // Form field update methods
  onNameChange(value: string): void {
    this.name.set(value);
  }

  onSalesTypeChange(value: 'single' | 'group'): void {
    this.salesType.set(value);
  }

  onDesignChange(value: number): void {
    this.designId.set(value);
  }

  onTypeChange(value: number): void {
    this.typeId.set(value);
  }

  onUnitChange(value: number): void {
    this.unitId.set(value);
  }

  onUnitOrderPriceChange(value: number): void {
    this.unitOrderPrice.set(value);
  }

  onOpeningStockQuantityChange(value: number): void {
    this.openingStockQuantity.set(value);
  }

  onOpeningStockUnitPriceChange(value: number): void {
    this.openingStockUnitPrice.set(value);
  }
}
