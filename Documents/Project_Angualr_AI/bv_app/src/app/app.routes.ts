import { Routes } from '@angular/router';
import { authGuard, loginGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login').then(m => m.Login),
    canActivate: [loginGuard]
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard').then(m => m.Dashboard),
    canActivate: [authGuard]
  },
  {
    path: 'materials',
    canActivate: [authGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./features/materials/material-list/material-list').then(m => m.MaterialList)
      },
      {
        path: 'new',
        loadComponent: () => import('./features/materials/material-form/material-form').then(m => m.MaterialForm)
      },
      {
        path: ':id',
        loadComponent: () => import('./features/materials/material-form/material-form').then(m => m.MaterialForm)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./features/materials/material-form/material-form').then(m => m.MaterialForm)
      }
    ]
  },
  {
    path: 'suppliers',
    loadComponent: () => import('./features/suppliers/supplier-list/supplier-list').then(m => m.SupplierList),
    canActivate: [authGuard]
  },
  {
    path: 'customers',
    loadComponent: () => import('./features/customers/customer-list/customer-list').then(m => m.CustomerList),
    canActivate: [authGuard]
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
