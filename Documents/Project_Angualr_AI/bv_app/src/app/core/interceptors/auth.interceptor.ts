import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);

  // Add credentials to all requests to include session cookies
  const authReq = req.clone({
    setHeaders: {
      'Content-Type': 'application/json'
    },
    withCredentials: true
  });

  return next(authReq).pipe(
    catchError(error => {
      // Handle authentication errors
      if (error.status === 401 || error.status === 403) {
        // Redirect to login page on authentication failure
        router.navigate(['/login']);
      }
      
      return throwError(() => error);
    })
  );
};
