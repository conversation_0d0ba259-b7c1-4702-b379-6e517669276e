import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';

export const csrfInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip CSRF for GET requests and external URLs
  if (req.method === 'GET' || !req.url.startsWith('/')) {
    return next(req);
  }

  // Get CSRF token from cookie
  const csrfToken = getCsrfTokenFromCookie();

  if (csrfToken) {
    const csrfReq = req.clone({
      setHeaders: {
        'X-CSRFToken': csrfToken
      }
    });
    return next(csrfReq);
  }

  return next(req);
};

function getCsrfTokenFromCookie(): string | null {
  const name = 'csrftoken';
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}
