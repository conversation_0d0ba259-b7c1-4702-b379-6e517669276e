import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { switchMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { environment } from '../../../environments/environment';

export const csrfInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip CSRF token for GET requests and external URLs
  if (req.method === 'GET' || !req.url.includes(environment.apiBaseUrl)) {
    return next(req);
  }

  // Check if CSRF token is already present
  if (req.headers.has('X-CSRFToken')) {
    return next(req);
  }

  const http = inject(HttpClient);

  // Get CSRF token from Django
  return http.get<{csrftoken: string}>(`${environment.apiUrl}/auth/csrf/`, {
    withCredentials: true
  }).pipe(
    switchMap(response => {
      const csrfToken = response.csrftoken;

      // Clone the request and add CSR<PERSON> token
      const csrfReq = req.clone({
        setHeaders: {
          'X-CSRFToken': csrfToken
        },
        withCredentials: true
      });

      return next(csrfReq);
    }),
    catchError(error => {
      console.error('Failed to get CSRF token:', error);
      // Continue with original request if CSRF token fetch fails
      const credentialsReq = req.clone({
        withCredentials: true
      });
      return next(credentialsReq);
    })
  );
};
