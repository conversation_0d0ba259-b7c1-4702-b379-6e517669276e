import { Injectable, inject, computed, signal } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { toSignal } from '@angular/core/rxjs-interop';

/**
 * Responsive Service
 * Provides reactive breakpoint detection using Angular Material's BreakpointObserver
 * Replaces CSS media queries with signal-based responsive design
 */
@Injectable({
  providedIn: 'root'
})
export class ResponsiveService {
  private breakpointObserver = inject(BreakpointObserver);

  // Convert BreakpointObserver observables to signals
  private handsetMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.Handset),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private tabletMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.Tablet),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private webMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.Web),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private handsetPortraitMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.HandsetPortrait),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private handsetLandscapeMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.HandsetLandscape),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private tabletPortraitMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.TabletPortrait),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private tabletLandscapeMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.TabletLandscape),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private webPortraitMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.WebPortrait),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  private webLandscapeMatches = toSignal(
    this.breakpointObserver.observe(Breakpoints.WebLandscape),
    { initialValue: { matches: false, breakpoints: {} } }
  );

  // Public computed signals for breakpoint detection
  readonly isHandset = computed(() => this.handsetMatches()?.matches ?? false);
  readonly isTablet = computed(() => this.tabletMatches()?.matches ?? false);
  readonly isDesktop = computed(() => this.webMatches()?.matches ?? false);

  readonly isHandsetPortrait = computed(() => this.handsetPortraitMatches()?.matches ?? false);
  readonly isHandsetLandscape = computed(() => this.handsetLandscapeMatches()?.matches ?? false);
  readonly isTabletPortrait = computed(() => this.tabletPortraitMatches()?.matches ?? false);
  readonly isTabletLandscape = computed(() => this.tabletLandscapeMatches()?.matches ?? false);
  readonly isWebPortrait = computed(() => this.webPortraitMatches()?.matches ?? false);
  readonly isWebLandscape = computed(() => this.webLandscapeMatches()?.matches ?? false);

  // Convenience computed signals
  readonly isMobile = computed(() => this.isHandset());
  readonly isSmallScreen = computed(() => this.isHandset() || this.isTablet());
  readonly isLargeScreen = computed(() => this.isDesktop());

  // Layout-specific computed signals
  readonly showSideNav = computed(() => !this.isHandset());
  readonly sideNavMode = computed(() => this.isHandset() ? 'over' : 'side');
  readonly showMobileMenu = computed(() => this.isHandset());

  // Grid column calculations
  readonly gridColumns = computed(() => {
    if (this.isHandset()) return 1;
    if (this.isTablet()) return 2;
    return 3;
  });

  readonly tableColumns = computed(() => {
    if (this.isHandset()) return ['name', 'actions'];
    if (this.isTablet()) return ['name', 'quantity', 'price', 'actions'];
    return ['name', 'design', 'quantity', 'price', 'value', 'actions'];
  });

  // Container classes for responsive design
  readonly containerClasses = computed(() => ({
    'responsive-mobile': this.isHandset(),
    'responsive-tablet': this.isTablet(),
    'responsive-desktop': this.isDesktop(),
    'responsive-small': this.isSmallScreen(),
    'responsive-large': this.isLargeScreen()
  }));

  // Padding and spacing calculations
  readonly containerPadding = computed(() => {
    if (this.isHandset()) return '16px';
    if (this.isTablet()) return '24px';
    return '32px';
  });

  readonly cardGap = computed(() => {
    if (this.isHandset()) return '12px';
    if (this.isTablet()) return '16px';
    return '24px';
  });

  /**
   * Check if a specific breakpoint matches
   * @param breakpoint - Angular Material breakpoint string
   * @returns Signal indicating if the breakpoint matches
   */
  isBreakpointMatched(breakpoint: string) {
    return toSignal(
      this.breakpointObserver.observe(breakpoint),
      { initialValue: { matches: false, breakpoints: {} } }
    );
  }

  /**
   * Check if multiple breakpoints match
   * @param breakpoints - Array of Angular Material breakpoint strings
   * @returns Signal indicating if any of the breakpoints match
   */
  areBreakpointsMatched(breakpoints: string[]) {
    return toSignal(
      this.breakpointObserver.observe(breakpoints),
      { initialValue: { matches: false, breakpoints: {} } }
    );
  }
}
