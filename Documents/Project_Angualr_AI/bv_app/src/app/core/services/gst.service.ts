import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface GstMaterial {
  id: number;
  material: {
    id: number;
    name: string;
    design: {
      name: string;
      color: string;
    };
  };
  hsn_code: string;
  gst_rate: number;
  created_at: string;
  updated_at: string;
}

export interface GstTax {
  id: number;
  name: string;
  rate: number;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface GstOrder {
  id: number;
  order_id: string;
  customer: {
    id: number;
    name: string;
    phone: string;
    gst_number?: string;
  };
  date: string;
  status: 'open' | 'closed' | 'cancelled';
  subtotal: number;
  total_gst: number;
  total_amount: number;
  created_at: string;
  updated_at: string;
}

export interface GstOrderItem {
  id: number;
  gst_order: number;
  gst_material: {
    id: number;
    material: {
      name: string;
      design: {
        name: string;
        color: string;
      };
    };
    hsn_code: string;
    gst_rate: number;
  };
  unit: {
    id: number;
    name: string;
  };
  quantity: number;
  unit_price: number;
  total_price: number;
  gst_amount: number;
  total_with_gst: number;
}

@Injectable({
  providedIn: 'root'
})
export class GstService {
  private http = inject(HttpClient);
  private readonly API_BASE = environment.apiUrl;

  // GST Materials
  getGstMaterials(materialId?: number): Observable<GstMaterial[]> {
    const params = materialId ? `?material=${materialId}` : '';
    return this.http.get<GstMaterial[]>(`${this.API_BASE}/gst-materials/${params}`);
  }

  getGstMaterial(id: number): Observable<GstMaterial> {
    return this.http.get<GstMaterial>(`${this.API_BASE}/gst-materials/${id}/`);
  }

  createGstMaterial(material: {
    material_id: number;
    hsn_code: string;
    gst_rate: number;
  }): Observable<GstMaterial> {
    return this.http.post<GstMaterial>(`${this.API_BASE}/gst-materials/`, material);
  }

  updateGstMaterial(id: number, material: Partial<GstMaterial>): Observable<GstMaterial> {
    return this.http.patch<GstMaterial>(`${this.API_BASE}/gst-materials/${id}/`, material);
  }

  deleteGstMaterial(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/gst-materials/${id}/`);
  }

  // GST Tax Rates
  getGstTaxes(): Observable<GstTax[]> {
    return this.http.get<GstTax[]>(`${this.API_BASE}/gst-taxes/`);
  }

  getGstTax(id: number): Observable<GstTax> {
    return this.http.get<GstTax>(`${this.API_BASE}/gst-taxes/${id}/`);
  }

  createGstTax(tax: {
    name: string;
    rate: number;
    description: string;
    is_active: boolean;
  }): Observable<GstTax> {
    return this.http.post<GstTax>(`${this.API_BASE}/gst-taxes/`, tax);
  }

  updateGstTax(id: number, tax: Partial<GstTax>): Observable<GstTax> {
    return this.http.patch<GstTax>(`${this.API_BASE}/gst-taxes/${id}/`, tax);
  }

  deleteGstTax(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/gst-taxes/${id}/`);
  }

  // GST Orders
  getGstOrders(params?: {
    customer?: number;
    status?: string;
    search?: string;
  }): Observable<GstOrder[]> {
    let queryParams = '';
    if (params) {
      const searchParams = new URLSearchParams();
      if (params.customer) searchParams.append('customer', params.customer.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.search) searchParams.append('search', params.search);
      queryParams = searchParams.toString() ? `?${searchParams.toString()}` : '';
    }
    return this.http.get<GstOrder[]>(`${this.API_BASE}/gst-orders/${queryParams}`);
  }

  getGstOrder(id: number): Observable<GstOrder> {
    return this.http.get<GstOrder>(`${this.API_BASE}/gst-orders/${id}/`);
  }

  createGstOrder(order: {
    customer_id: number;
    status: 'open' | 'closed' | 'cancelled';
    items: {
      gst_material_id: number;
      unit_id: number;
      quantity: number;
      unit_price: number;
    }[];
  }): Observable<GstOrder> {
    return this.http.post<GstOrder>(`${this.API_BASE}/gst-orders/`, order);
  }

  updateGstOrder(id: number, order: Partial<GstOrder>): Observable<GstOrder> {
    return this.http.patch<GstOrder>(`${this.API_BASE}/gst-orders/${id}/`, order);
  }

  deleteGstOrder(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/gst-orders/${id}/`);
  }

  // GST Order Items
  getGstOrderItems(orderId: string): Observable<GstOrderItem[]> {
    return this.http.get<GstOrderItem[]>(`${this.API_BASE}/gst-order-items/?order_id=${orderId}`);
  }

  addGstOrderItem(item: {
    gst_order_id: number;
    gst_material_id: number;
    unit_id: number;
    quantity: number;
    unit_price: number;
  }): Observable<GstOrderItem> {
    return this.http.post<GstOrderItem>(`${this.API_BASE}/gst-order-items/`, item);
  }

  updateGstOrderItem(id: number, item: Partial<GstOrderItem>): Observable<GstOrderItem> {
    return this.http.patch<GstOrderItem>(`${this.API_BASE}/gst-order-items/${id}/`, item);
  }

  deleteGstOrderItem(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/gst-order-items/${id}/`);
  }
}
