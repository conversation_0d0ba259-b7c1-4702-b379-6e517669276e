import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface Order {
  id: number;
  order_id: string;
  customer: {
    id: number;
    name: string;
    phone: string;
  };
  date: string;
  status: 'open' | 'closed' | 'cancelled';
  hst_total_order_amt: number;
  total_order_amt: number;
  total_order_profit: number;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: number;
  order: number;
  material: {
    id: number;
    name: string;
    design: {
      name: string;
      color: string;
    };
  };
  unit: {
    id: number;
    name: string;
  };
  quantity: number;
  unit_price: number;
  total_price: number;
  profit: number;
}

export interface OrderCreateRequest {
  customer_id: number;
  status: 'open' | 'closed' | 'cancelled';
  items: {
    material_id: number;
    unit_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private http = inject(HttpClient);
  private readonly API_BASE = `${environment.apiUrl}/orders`;

  getOrders(params?: {
    customer?: number;
    status?: string;
    search?: string;
  }): Observable<Order[]> {
    let queryParams = '';
    if (params) {
      const searchParams = new URLSearchParams();
      if (params.customer) searchParams.append('customer', params.customer.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.search) searchParams.append('search', params.search);
      queryParams = searchParams.toString() ? `?${searchParams.toString()}` : '';
    }
    return this.http.get<Order[]>(`${this.API_BASE}/${queryParams}`);
  }

  getOrder(id: number): Observable<Order> {
    return this.http.get<Order>(`${this.API_BASE}/${id}/`);
  }

  createOrder(order: OrderCreateRequest): Observable<Order> {
    return this.http.post<Order>(`${this.API_BASE}/`, order);
  }

  updateOrder(id: number, order: Partial<OrderCreateRequest>): Observable<Order> {
    return this.http.patch<Order>(`${this.API_BASE}/${id}/`, order);
  }

  deleteOrder(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${id}/`);
  }

  getOrderItems(orderId: string): Observable<OrderItem[]> {
    return this.http.get<OrderItem[]>(`${environment.apiUrl}/order-items/?order_id=${orderId}`);
  }

  addOrderItem(item: {
    order_id: number;
    material_id: number;
    unit_id: number;
    quantity: number;
    unit_price: number;
  }): Observable<OrderItem> {
    return this.http.post<OrderItem>(`${environment.apiUrl}/order-items/`, item);
  }

  updateOrderItem(id: number, item: Partial<OrderItem>): Observable<OrderItem> {
    return this.http.patch<OrderItem>(`${environment.apiUrl}/order-items/${id}/`, item);
  }

  deleteOrderItem(id: number): Observable<void> {
    return this.http.delete<void>(`${environment.apiUrl}/order-items/${id}/`);
  }
}
