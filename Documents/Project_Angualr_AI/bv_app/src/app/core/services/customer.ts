import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface CustomerType {
  id: number;
  type: string;
}

export interface Customer {
  id: number;
  name: string;
  phone: string;
  address: string;
  opening_amt: number;
  type: CustomerType;
  date: string;
  total_payment: number;
  total_order_amt: number;
  total_order_return_amt: number;
  total_freight: number;
  total_discount: number;
  remaining_amount: number;
  total_orders: number;
  hst_total_payment: number;
  hst_total_profit: number;
  hst_total_order_amt: number;
  hst_total_order_return_amt: number;
}

export interface CustomerCreateRequest {
  name: string;
  phone: string;
  address: string;
  opening_amt: number;
  type_id: number;
}

export interface Order {
  order_id: string;
  customer: Customer;
  date: string;
  status: 'open' | 'delivered';
  freight: number;
  entry_type: 'new' | 'change' | 'delete' | 'revert';
  version: number;
  parent_id?: string;
  items: OrderItem[];
  total_amt: number;
  total_profit: number;
  total_items: number;
}

export interface OrderItem {
  id: number;
  material: any;
  unit: any;
  quantity: number;
  unit_price: number;
}

export interface OrderCreateRequest {
  customer_id: number;
  freight: number;
  items: {
    material_id: number;
    unit_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface CustomerPayment {
  id: number;
  customer: Customer;
  date: string;
  amount: number;
  payment_type: string;
  remarks: string;
}

export interface CustomerPaymentCreateRequest {
  customer_id: number;
  amount: number;
  payment_type: string;
  remarks: string;
}

export interface OrderReturn {
  id: number;
  customer: Customer;
  material: any;
  unit: any;
  quantity: number;
  unit_price: number;
  date: string;
  remarks: string;
}

export interface OrderReturnCreateRequest {
  customer_id: number;
  material_id: number;
  unit_id: number;
  quantity: number;
  unit_price: number;
  remarks: string;
}

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private http = inject(HttpClient);
  private readonly API_BASE = `${environment.apiUrl}/customers`;

  // Customer Types
  getCustomerTypes(): Observable<CustomerType[]> {
    return this.http.get<CustomerType[]>(`${environment.apiUrl}/customer-types/`);
  }

  createCustomerType(customerType: Omit<CustomerType, 'id'>): Observable<CustomerType> {
    return this.http.post<CustomerType>(`${environment.apiUrl}/customer-types/`, customerType);
  }

  // Customer CRUD operations
  getCustomers(): Observable<Customer[]> {
    return this.http.get<Customer[]>(`${this.API_BASE}/`);
  }

  getCustomer(id: number): Observable<Customer> {
    return this.http.get<Customer>(`${this.API_BASE}/${id}/`);
  }

  createCustomer(customer: CustomerCreateRequest): Observable<Customer> {
    return this.http.post<Customer>(`${this.API_BASE}/`, customer);
  }

  updateCustomer(id: number, customer: Partial<CustomerCreateRequest>): Observable<Customer> {
    return this.http.patch<Customer>(`${this.API_BASE}/${id}/`, customer);
  }

  deleteCustomer(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${id}/`);
  }

  // Search customers
  searchCustomers(params: { search?: string; type?: number }): Observable<Customer[]> {
    let queryParams = new URLSearchParams();
    if (params.search) queryParams.set('search', params.search);
    if (params.type) queryParams.set('type', params.type.toString());

    const url = `${this.API_BASE}/?${queryParams.toString()}`;
    return this.http.get<Customer[]>(url);
  }

  // Customer related data
  getCustomerOrders(customerId: number): Observable<Order[]> {
    return this.http.get<Order[]>(`${this.API_BASE}/${customerId}/orders/`);
  }

  getCustomerPayments(customerId: number): Observable<CustomerPayment[]> {
    return this.http.get<CustomerPayment[]>(`${this.API_BASE}/${customerId}/payments/`);
  }

  getCustomerReturns(customerId: number): Observable<OrderReturn[]> {
    return this.http.get<OrderReturn[]>(`${this.API_BASE}/${customerId}/returns/`);
  }

  constructor() { }
}
