import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface DashboardStats {
  materials: {
    total: number;
    low_stock: number;
  };
  suppliers: {
    total: number;
    pending_orders: number;
  };
  customers: {
    total: number;
    pending_orders: number;
  };
  financial: {
    total_order_value: number;
    total_stock_value: number;
  };
}

export interface RecentActivities {
  recent_orders: any[];
  recent_stock_orders: any[];
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private http = inject(HttpClient);
  private readonly API_BASE = `${environment.apiUrl}/dashboard`;

  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.API_BASE}/stats/`);
  }

  getRecentActivities(): Observable<RecentActivities> {
    return this.http.get<RecentActivities>(`${this.API_BASE}/recent_activities/`);
  }
}
