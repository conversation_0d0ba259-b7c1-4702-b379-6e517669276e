import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface Supplier {
  id: number;
  name: string;
  phone: string;
  address: string;
  opening_amt: number;
  date: string;
  total_payment: number;
  total_stock_return_amt: number;
  total_balance: number;
  hst_total_order_amt: number;
  hst_transport_charges: number;
  hst_unloading_charges: number;
  hst_total_return_amt: number;
  hst_total_payment: number;
}

export interface SupplierCreateRequest {
  name: string;
  phone: string;
  address: string;
  opening_amt: number;
}

export interface StockOrder {
  order_id: string;
  supplier: Supplier;
  date: string;
  status: 'open' | 'delivered';
  transport_charges: number;
  unloading_charges: number;
  entry_type: 'new' | 'change' | 'delete' | 'revert';
  version: number;
  parent_id?: string;
  items: StockOrderItem[];
  total_amt: number;
  total_items: number;
}

export interface StockOrderItem {
  id: number;
  material: any;
  unit: any;
  quantity: number;
  unit_price: number;
}

export interface StockOrderCreateRequest {
  supplier_id: number;
  transport_charges: number;
  unloading_charges: number;
  items: {
    material_id: number;
    unit_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface SupplierPayment {
  id: number;
  supplier: Supplier;
  date: string;
  amount: number;
  payment_type: string;
  remarks: string;
}

export interface SupplierPaymentCreateRequest {
  supplier_id: number;
  amount: number;
  payment_type: string;
  remarks: string;
}

export interface StockReturn {
  id: number;
  supplier: Supplier;
  material: any;
  unit: any;
  quantity: number;
  unit_price: number;
  date: string;
  remarks: string;
}

export interface StockReturnCreateRequest {
  supplier_id: number;
  material_id: number;
  unit_id: number;
  quantity: number;
  unit_price: number;
  remarks: string;
}

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private http = inject(HttpClient);
  private readonly API_BASE = `${environment.apiUrl}/suppliers`;

  // Supplier CRUD operations
  getSuppliers(): Observable<Supplier[]> {
    return this.http.get<Supplier[]>(`${this.API_BASE}/`);
  }

  getSupplier(id: number): Observable<Supplier> {
    return this.http.get<Supplier>(`${this.API_BASE}/${id}/`);
  }

  createSupplier(supplier: SupplierCreateRequest): Observable<Supplier> {
    return this.http.post<Supplier>(`${this.API_BASE}/`, supplier);
  }

  updateSupplier(id: number, supplier: Partial<SupplierCreateRequest>): Observable<Supplier> {
    return this.http.patch<Supplier>(`${this.API_BASE}/${id}/`, supplier);
  }

  deleteSupplier(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${id}/`);
  }

  // Search suppliers
  searchSuppliers(search: string): Observable<Supplier[]> {
    return this.http.get<Supplier[]>(`${this.API_BASE}/?search=${search}`);
  }

  // Supplier related data
  getSupplierStockOrders(supplierId: number): Observable<StockOrder[]> {
    return this.http.get<StockOrder[]>(`${this.API_BASE}/${supplierId}/stock_orders/`);
  }

  getSupplierPayments(supplierId: number): Observable<SupplierPayment[]> {
    return this.http.get<SupplierPayment[]>(`${this.API_BASE}/${supplierId}/payments/`);
  }

  getSupplierReturns(supplierId: number): Observable<StockReturn[]> {
    return this.http.get<StockReturn[]>(`${this.API_BASE}/${supplierId}/returns/`);
  }

  // Stock Orders
  getStockOrders(params?: { supplier?: number; status?: string }): Observable<StockOrder[]> {
    let url = `${environment.apiUrl}/stock-orders/`;
    if (params) {
      const queryParams = new URLSearchParams();
      if (params.supplier) queryParams.set('supplier', params.supplier.toString());
      if (params.status) queryParams.set('status', params.status);
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }
    return this.http.get<StockOrder[]>(url);
  }

  getStockOrder(orderId: string): Observable<StockOrder> {
    return this.http.get<StockOrder>(`${environment.apiUrl}/stock-orders/${orderId}/`);
  }

  createStockOrder(order: StockOrderCreateRequest): Observable<StockOrder> {
    return this.http.post<StockOrder>(`${environment.apiUrl}/stock-orders/`, order);
  }

  updateStockOrder(orderId: string, order: Partial<StockOrderCreateRequest>): Observable<StockOrder> {
    return this.http.patch<StockOrder>(`${environment.apiUrl}/stock-orders/${orderId}/`, order);
  }

  deleteStockOrder(orderId: string): Observable<void> {
    return this.http.delete<void>(`${environment.apiUrl}/stock-orders/${orderId}/`);
  }

  // Stock Order Items
  getStockOrderItems(orderId: string): Observable<StockOrderItem[]> {
    return this.http.get<StockOrderItem[]>(`${environment.apiUrl}/stock-order-items/?order_id=${orderId}`);
  }

  createStockOrderItem(item: Omit<StockOrderItem, 'id'>): Observable<StockOrderItem> {
    return this.http.post<StockOrderItem>(`${environment.apiUrl}/stock-order-items/`, item);
  }

  updateStockOrderItem(id: number, item: Partial<StockOrderItem>): Observable<StockOrderItem> {
    return this.http.patch<StockOrderItem>(`/api/stock-order-items/${id}/`, item);
  }

  deleteStockOrderItem(id: number): Observable<void> {
    return this.http.delete<void>(`/api/stock-order-items/${id}/`);
  }

  // Supplier Payments
  getSupplierPaymentsList(supplierId?: number): Observable<SupplierPayment[]> {
    let url = '/api/supplier-payments/';
    if (supplierId) {
      url += `?supplier=${supplierId}`;
    }
    return this.http.get<SupplierPayment[]>(url);
  }

  createSupplierPayment(payment: SupplierPaymentCreateRequest): Observable<SupplierPayment> {
    return this.http.post<SupplierPayment>('/api/supplier-payments/', payment);
  }

  updateSupplierPayment(id: number, payment: Partial<SupplierPaymentCreateRequest>): Observable<SupplierPayment> {
    return this.http.patch<SupplierPayment>(`/api/supplier-payments/${id}/`, payment);
  }

  deleteSupplierPayment(id: number): Observable<void> {
    return this.http.delete<void>(`/api/supplier-payments/${id}/`);
  }

  // Stock Returns
  getStockReturns(supplierId?: number): Observable<StockReturn[]> {
    let url = '/api/stock-returns/';
    if (supplierId) {
      url += `?supplier=${supplierId}`;
    }
    return this.http.get<StockReturn[]>(url);
  }

  createStockReturn(stockReturn: StockReturnCreateRequest): Observable<StockReturn> {
    return this.http.post<StockReturn>('/api/stock-returns/', stockReturn);
  }

  updateStockReturn(id: number, stockReturn: Partial<StockReturnCreateRequest>): Observable<StockReturn> {
    return this.http.patch<StockReturn>(`/api/stock-returns/${id}/`, stockReturn);
  }

  deleteStockReturn(id: number): Observable<void> {
    return this.http.delete<void>(`/api/stock-returns/${id}/`);
  }

  constructor() { }
}
