from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .api_views import (
    AuthViewSet, MaterialViewSet, DesignViewSet, UnitViewSet, MaterialTypeViewSet,
    SupplierViewSet, StockOrderViewSet, StockOrderItemViewSet, SupplierPaymentViewSet,
    StockReturnViewSet, CustomerTypeViewSet, CustomerViewSet, OrderViewSet,
    OrderItemViewSet, CustomerPaymentViewSet, OrderReturnViewSet,
    GroupMaterialsItemViewSet, GstMaterialViewSet, GstTaxViewSet,
    GstOrderViewSet, GstOrderItemViewSet, DashboardViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()

# Authentication
router.register(r'auth', AuthViewSet, basename='auth')

# Dashboard
router.register(r'dashboard', DashboardViewSet, basename='dashboard')

# Materials Management
router.register(r'materials', MaterialViewSet)
router.register(r'designs', DesignViewSet)
router.register(r'units', UnitViewSet)
router.register(r'material-types', MaterialTypeViewSet)
router.register(r'group-materials-items', GroupMaterialsItemViewSet)

# Suppliers Management
router.register(r'suppliers', SupplierViewSet)
router.register(r'stock-orders', StockOrderViewSet)
router.register(r'stock-order-items', StockOrderItemViewSet)
router.register(r'supplier-payments', SupplierPaymentViewSet)
router.register(r'stock-returns', StockReturnViewSet)

# Customers Management
router.register(r'customers', CustomerViewSet)
router.register(r'customer-types', CustomerTypeViewSet)
router.register(r'orders', OrderViewSet)
router.register(r'order-items', OrderItemViewSet)
router.register(r'customer-payments', CustomerPaymentViewSet)
router.register(r'order-returns', OrderReturnViewSet)

# GST Management
router.register(r'gst-materials', GstMaterialViewSet)
router.register(r'gst-taxes', GstTaxViewSet)
router.register(r'gst-orders', GstOrderViewSet)
router.register(r'gst-order-items', GstOrderItemViewSet)

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
