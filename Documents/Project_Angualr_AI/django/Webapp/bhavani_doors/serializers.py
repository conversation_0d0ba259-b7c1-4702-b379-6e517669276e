from rest_framework import serializers
from .models import (
    material, design, unit, material_type, supplier, stock_order, stock_order_item,
    supplier_payment, stock_return, customer_type, customer, order, order_item,
    customer_payment, order_return, group_materials_item, gst_material, gst_tax,
    gst_order, gst_order_item
)


class DesignSerializer(serializers.ModelSerializer):
    class Meta:
        model = design
        fields = '__all__'


class UnitSerializer(serializers.ModelSerializer):
    class Meta:
        model = unit
        fields = '__all__'


class MaterialTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = material_type
        fields = '__all__'


class MaterialSerializer(serializers.ModelSerializer):
    design = DesignSerializer(read_only=True)
    unit = UnitSerializer(read_only=True)
    type = MaterialTypeSerializer(read_only=True)
    design_id = serializers.IntegerField(write_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    type_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = material
        fields = [
            'id', 'name', 'sales_type', 'design', 'type', 'unit',
            'unit_order_price', 'opening_stock_quantity', 'opening_stock_unit_price',
            'available_quantity', 'stock_unit_price', 'total_stock_value',
            'total_profit', 'last_updated', 'design_id', 'unit_id', 'type_id'
        ]
        read_only_fields = ['available_quantity', 'stock_unit_price', 'total_stock_value', 'total_profit', 'last_updated']


class MaterialCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = material
        fields = [
            'name', 'sales_type', 'design', 'type', 'unit',
            'unit_order_price', 'opening_stock_quantity', 'opening_stock_unit_price'
        ]


class SupplierSerializer(serializers.ModelSerializer):
    total_payment = serializers.ReadOnlyField()
    total_stock_return_amt = serializers.ReadOnlyField()
    total_balance = serializers.ReadOnlyField()
    
    class Meta:
        model = supplier
        fields = [
            'id', 'name', 'phone', 'address', 'opening_amt', 'date',
            'total_payment', 'total_stock_return_amt', 'total_balance',
            'hst_total_order_amt', 'hst_transport_charges', 'hst_unloading_charges',
            'hst_total_return_amt', 'hst_total_payment'
        ]
        read_only_fields = ['date', 'hst_total_order_amt', 'hst_transport_charges', 
                           'hst_unloading_charges', 'hst_total_return_amt', 'hst_total_payment']


class StockOrderItemSerializer(serializers.ModelSerializer):
    material = MaterialSerializer(read_only=True)
    material_id = serializers.IntegerField(write_only=True)
    unit = UnitSerializer(read_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = stock_order_item
        fields = [
            'id', 'material', 'unit', 'quantity', 'unit_price',
            'material_id', 'unit_id'
        ]


class StockOrderSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)
    supplier_id = serializers.IntegerField(write_only=True)
    items = StockOrderItemSerializer(source='stock_order_item_set', many=True, read_only=True)
    total_amt = serializers.ReadOnlyField()
    total_items = serializers.ReadOnlyField()
    
    class Meta:
        model = stock_order
        fields = [
            'order_id', 'supplier', 'date', 'status', 'transport_charges',
            'unloading_charges', 'entry_type', 'version', 'parent_id',
            'supplier_id', 'items', 'total_amt', 'total_items'
        ]
        read_only_fields = ['order_id', 'date', 'version']


class SupplierPaymentSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)
    supplier_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = supplier_payment
        fields = [
            'id', 'supplier', 'date', 'amount', 'payment_type', 'remarks',
            'supplier_id'
        ]
        read_only_fields = ['date']


class StockReturnSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)
    supplier_id = serializers.IntegerField(write_only=True)
    material = MaterialSerializer(read_only=True)
    material_id = serializers.IntegerField(write_only=True)
    unit = UnitSerializer(read_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = stock_return
        fields = [
            'id', 'supplier', 'material', 'unit', 'quantity', 'unit_price',
            'date', 'remarks', 'supplier_id', 'material_id', 'unit_id'
        ]
        read_only_fields = ['date']


class CustomerTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = customer_type
        fields = '__all__'


class CustomerSerializer(serializers.ModelSerializer):
    type = CustomerTypeSerializer(read_only=True)
    type_id = serializers.IntegerField(write_only=True)
    total_payment = serializers.ReadOnlyField()
    total_order_amt = serializers.ReadOnlyField()
    total_order_return_amt = serializers.ReadOnlyField()
    total_freight = serializers.ReadOnlyField()
    total_discount = serializers.ReadOnlyField()
    remaining_amount = serializers.ReadOnlyField()
    total_orders = serializers.ReadOnlyField()
    
    class Meta:
        model = customer
        fields = [
            'id', 'name', 'phone', 'address', 'opening_amt', 'type', 'date',
            'total_payment', 'total_order_amt', 'total_order_return_amt',
            'total_freight', 'total_discount', 'remaining_amount', 'total_orders',
            'type_id', 'hst_total_payment', 'hst_total_profit', 'hst_total_order_amt',
            'hst_total_order_return_amt'
        ]
        read_only_fields = ['date', 'hst_total_payment', 'hst_total_profit', 
                           'hst_total_order_amt', 'hst_total_order_return_amt']


class OrderItemSerializer(serializers.ModelSerializer):
    material = MaterialSerializer(read_only=True)
    material_id = serializers.IntegerField(write_only=True)
    unit = UnitSerializer(read_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = order_item
        fields = [
            'id', 'material', 'unit', 'quantity', 'unit_price',
            'material_id', 'unit_id'
        ]


class OrderSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    customer_id = serializers.IntegerField(write_only=True)
    items = OrderItemSerializer(source='order_item_set', many=True, read_only=True)
    total_amt = serializers.ReadOnlyField()
    total_profit = serializers.ReadOnlyField()
    total_items = serializers.ReadOnlyField()
    
    class Meta:
        model = order
        fields = [
            'order_id', 'customer', 'date', 'status', 'freight', 'entry_type',
            'version', 'parent_id', 'customer_id', 'items', 'total_amt',
            'total_profit', 'total_items'
        ]
        read_only_fields = ['order_id', 'date', 'version']


class CustomerPaymentSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    customer_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = customer_payment
        fields = [
            'id', 'customer', 'date', 'amount', 'payment_type', 'remarks',
            'customer_id'
        ]
        read_only_fields = ['date']


class OrderReturnSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    customer_id = serializers.IntegerField(write_only=True)
    material = MaterialSerializer(read_only=True)
    material_id = serializers.IntegerField(write_only=True)
    unit = UnitSerializer(read_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = order_return
        fields = [
            'id', 'customer', 'material', 'unit', 'quantity', 'unit_price',
            'date', 'remarks', 'customer_id', 'material_id', 'unit_id'
        ]
        read_only_fields = ['date']


class GroupMaterialsItemSerializer(serializers.ModelSerializer):
    group_material = MaterialSerializer(read_only=True)
    group_material_id = serializers.IntegerField(write_only=True)
    single_material = MaterialSerializer(read_only=True)
    single_material_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = group_materials_item
        fields = [
            'id', 'group_material', 'single_material', 'quantity',
            'group_material_id', 'single_material_id'
        ]


class GstMaterialSerializer(serializers.ModelSerializer):
    material = MaterialSerializer(read_only=True)
    material_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = gst_material
        fields = [
            'id', 'material', 'hsn_code', 'gst_rate', 'material_id'
        ]


class GstTaxSerializer(serializers.ModelSerializer):
    class Meta:
        model = gst_tax
        fields = '__all__'


class GstOrderItemSerializer(serializers.ModelSerializer):
    gst_material = GstMaterialSerializer(read_only=True)
    gst_material_id = serializers.IntegerField(write_only=True)
    unit = UnitSerializer(read_only=True)
    unit_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = gst_order_item
        fields = [
            'id', 'gst_material', 'unit', 'quantity', 'unit_price',
            'gst_material_id', 'unit_id'
        ]


class GstOrderSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    customer_id = serializers.IntegerField(write_only=True)
    items = GstOrderItemSerializer(source='gst_order_item_set', many=True, read_only=True)
    total_amt = serializers.ReadOnlyField()
    total_gst = serializers.ReadOnlyField()
    total_items = serializers.ReadOnlyField()
    
    class Meta:
        model = gst_order
        fields = [
            'order_id', 'customer', 'date', 'status', 'freight', 'entry_type',
            'version', 'parent_id', 'customer_id', 'items', 'total_amt',
            'total_gst', 'total_items'
        ]
        read_only_fields = ['order_id', 'date', 'version']
