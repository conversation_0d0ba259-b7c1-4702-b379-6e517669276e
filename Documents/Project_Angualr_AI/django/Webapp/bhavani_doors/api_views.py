from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.middleware.csrf import get_token
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db.models import Q
from .models import (
    material, design, unit, material_type, supplier, stock_order, stock_order_item,
    supplier_payment, stock_return, customer_type, customer, order, order_item,
    customer_payment, order_return, group_materials_item, gst_material, gst_tax,
    gst_order, gst_order_item
)
from .serializers import (
    MaterialSerializer, DesignSerializer, UnitSerializer, MaterialTypeSerializer,
    SupplierSerializer, StockOrderSerializer, StockOrderItemSerializer,
    SupplierPaymentSerializer, StockReturnSerializer, CustomerTypeSerializer,
    CustomerSerializer, OrderSerializer, OrderItemSerializer, CustomerPaymentSerializer,
    OrderReturnSerializer, GroupMaterialsItemSerializer, GstMaterialSerializer,
    GstTaxSerializer, GstOrderSerializer, GstOrderItemSerializer
)


class AuthViewSet(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]

    @action(detail=False, methods=['post'])
    def login(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'success': False,
                'message': 'Username and password required'
            }, status=status.HTTP_400_BAD_REQUEST)

        user = authenticate(request, username=username, password=password)
        if user and user.is_active and user.is_staff:
            login(request, user)
            return Response({
                'success': True,
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser
                }
            })
        else:
            return Response({
                'success': False,
                'message': 'Invalid credentials or insufficient permissions'
            }, status=status.HTTP_401_UNAUTHORIZED)
    
    @action(detail=False, methods=['post'])
    def logout(self, request):
        logout(request)
        return Response({'success': True, 'message': 'Logged out successfully'})
    
    @action(detail=False, methods=['get'])
    def user(self, request):
        if request.user.is_authenticated and request.user.is_staff:
            return Response({
                'id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
                'first_name': request.user.first_name,
                'last_name': request.user.last_name,
                'is_staff': request.user.is_staff,
                'is_superuser': request.user.is_superuser
            })
        else:
            return Response({'error': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)
    
    @action(detail=False, methods=['get'])
    def csrf(self, request):
        return Response({'csrftoken': get_token(request)})


class DesignViewSet(viewsets.ModelViewSet):
    queryset = design.objects.all()
    serializer_class = DesignSerializer
    permission_classes = [IsAuthenticated]


class UnitViewSet(viewsets.ModelViewSet):
    queryset = unit.objects.all()
    serializer_class = UnitSerializer
    permission_classes = [IsAuthenticated]


class MaterialTypeViewSet(viewsets.ModelViewSet):
    queryset = material_type.objects.all()
    serializer_class = MaterialTypeSerializer
    permission_classes = [IsAuthenticated]


class MaterialViewSet(viewsets.ModelViewSet):
    queryset = material.objects.all()
    serializer_class = MaterialSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = material.objects.all()
        search = self.request.query_params.get('search', None)
        sales_type = self.request.query_params.get('sales_type', None)
        material_type_id = self.request.query_params.get('type', None)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(design__name__icontains=search) |
                Q(design__color__icontains=search)
            )
        
        if sales_type:
            queryset = queryset.filter(sales_type=sales_type)
            
        if material_type_id:
            queryset = queryset.filter(type_id=material_type_id)
        
        return queryset.select_related('design', 'unit', 'type')
    
    @action(detail=True, methods=['get'])
    def stock_history(self, request, pk=None):
        material_obj = self.get_object()
        # Return stock order items for this material
        stock_items = stock_order_item.objects.filter(material=material_obj).select_related('stock_order__supplier')
        serializer = StockOrderItemSerializer(stock_items, many=True)
        return Response(serializer.data)


class SupplierViewSet(viewsets.ModelViewSet):
    queryset = supplier.objects.all()
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = supplier.objects.all()
        search = self.request.query_params.get('search', None)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(phone__icontains=search)
            )
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def stock_orders(self, request, pk=None):
        supplier_obj = self.get_object()
        orders = stock_order.objects.filter(supplier=supplier_obj)
        serializer = StockOrderSerializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        supplier_obj = self.get_object()
        payments = supplier_payment.objects.filter(supplier=supplier_obj)
        serializer = SupplierPaymentSerializer(payments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def returns(self, request, pk=None):
        supplier_obj = self.get_object()
        returns = stock_return.objects.filter(supplier=supplier_obj)
        serializer = StockReturnSerializer(returns, many=True)
        return Response(serializer.data)


class StockOrderViewSet(viewsets.ModelViewSet):
    queryset = stock_order.objects.all()
    serializer_class = StockOrderSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = stock_order.objects.all()
        supplier_id = self.request.query_params.get('supplier', None)
        status_filter = self.request.query_params.get('status', None)
        
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
            
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.select_related('supplier')


class StockOrderItemViewSet(viewsets.ModelViewSet):
    queryset = stock_order_item.objects.all()
    serializer_class = StockOrderItemSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = stock_order_item.objects.all()
        order_id = self.request.query_params.get('order_id', None)
        
        if order_id:
            queryset = queryset.filter(stock_order__order_id=order_id)
        
        return queryset.select_related('material', 'unit', 'stock_order')


class SupplierPaymentViewSet(viewsets.ModelViewSet):
    queryset = supplier_payment.objects.all()
    serializer_class = SupplierPaymentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = supplier_payment.objects.all()
        supplier_id = self.request.query_params.get('supplier', None)
        
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
        
        return queryset.select_related('supplier')


class StockReturnViewSet(viewsets.ModelViewSet):
    queryset = stock_return.objects.all()
    serializer_class = StockReturnSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = stock_return.objects.all()
        supplier_id = self.request.query_params.get('supplier', None)
        
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
        
        return queryset.select_related('supplier', 'material', 'unit')


class CustomerTypeViewSet(viewsets.ModelViewSet):
    queryset = customer_type.objects.all()
    serializer_class = CustomerTypeSerializer
    permission_classes = [IsAuthenticated]


class CustomerViewSet(viewsets.ModelViewSet):
    queryset = customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = customer.objects.all()
        search = self.request.query_params.get('search', None)
        customer_type_id = self.request.query_params.get('type', None)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(phone__icontains=search)
            )
        
        if customer_type_id:
            queryset = queryset.filter(type_id=customer_type_id)
        
        return queryset.select_related('type')
    
    @action(detail=True, methods=['get'])
    def orders(self, request, pk=None):
        customer_obj = self.get_object()
        orders = order.objects.filter(customer=customer_obj)
        serializer = OrderSerializer(orders, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        customer_obj = self.get_object()
        payments = customer_payment.objects.filter(customer=customer_obj)
        serializer = CustomerPaymentSerializer(payments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def returns(self, request, pk=None):
        customer_obj = self.get_object()
        returns = order_return.objects.filter(customer=customer_obj)
        serializer = OrderReturnSerializer(returns, many=True)
        return Response(serializer.data)


class OrderViewSet(viewsets.ModelViewSet):
    queryset = order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = order.objects.all()
        customer_id = self.request.query_params.get('customer', None)
        status_filter = self.request.query_params.get('status', None)
        
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
            
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.select_related('customer')


class OrderItemViewSet(viewsets.ModelViewSet):
    queryset = order_item.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = order_item.objects.all()
        order_id = self.request.query_params.get('order_id', None)
        
        if order_id:
            queryset = queryset.filter(order__order_id=order_id)
        
        return queryset.select_related('material', 'unit', 'order')


class CustomerPaymentViewSet(viewsets.ModelViewSet):
    queryset = customer_payment.objects.all()
    serializer_class = CustomerPaymentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = customer_payment.objects.all()
        customer_id = self.request.query_params.get('customer', None)
        
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        return queryset.select_related('customer')


class OrderReturnViewSet(viewsets.ModelViewSet):
    queryset = order_return.objects.all()
    serializer_class = OrderReturnSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = order_return.objects.all()
        customer_id = self.request.query_params.get('customer', None)
        
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        return queryset.select_related('customer', 'material', 'unit')


class GroupMaterialsItemViewSet(viewsets.ModelViewSet):
    queryset = group_materials_item.objects.all()
    serializer_class = GroupMaterialsItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = group_materials_item.objects.all()
        group_material_id = self.request.query_params.get('group_material', None)

        if group_material_id:
            queryset = queryset.filter(group_material_id=group_material_id)

        return queryset.select_related('group_material', 'single_material')


class GstMaterialViewSet(viewsets.ModelViewSet):
    queryset = gst_material.objects.all()
    serializer_class = GstMaterialSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = gst_material.objects.all()
        material_id = self.request.query_params.get('material', None)

        if material_id:
            queryset = queryset.filter(material_id=material_id)

        return queryset.select_related('material')


class GstTaxViewSet(viewsets.ModelViewSet):
    queryset = gst_tax.objects.all()
    serializer_class = GstTaxSerializer
    permission_classes = [IsAuthenticated]


class GstOrderViewSet(viewsets.ModelViewSet):
    queryset = gst_order.objects.all()
    serializer_class = GstOrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = gst_order.objects.all()
        customer_id = self.request.query_params.get('customer', None)
        status_filter = self.request.query_params.get('status', None)

        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.select_related('customer')


class GstOrderItemViewSet(viewsets.ModelViewSet):
    queryset = gst_order_item.objects.all()
    serializer_class = GstOrderItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = gst_order_item.objects.all()
        order_id = self.request.query_params.get('order_id', None)

        if order_id:
            queryset = queryset.filter(gst_order__order_id=order_id)

        return queryset.select_related('gst_material', 'unit', 'gst_order')


class DashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get dashboard statistics"""
        from django.db.models import Sum, Count

        # Materials stats
        total_materials = material.objects.count()
        # Calculate low stock materials by checking available_quantity property
        low_stock_materials = 0
        for mat in material.objects.all():
            if mat.available_quantity < 10:
                low_stock_materials += 1

        # Suppliers stats
        total_suppliers = supplier.objects.count()
        pending_stock_orders = stock_order.objects.filter(status='open').count()

        # Customers stats
        total_customers = customer.objects.count()
        pending_orders = order.objects.filter(status='open').count()

        # Financial stats
        total_order_value = order.objects.aggregate(
            total=Sum('hst_total_order_amt')
        )['total'] or 0

        # Calculate total stock value using property method
        total_stock_value = 0
        for mat in material.objects.all():
            total_stock_value += mat.total_stock_value

        return Response({
            'materials': {
                'total': total_materials,
                'low_stock': low_stock_materials
            },
            'suppliers': {
                'total': total_suppliers,
                'pending_orders': pending_stock_orders
            },
            'customers': {
                'total': total_customers,
                'pending_orders': pending_orders
            },
            'financial': {
                'total_order_value': float(total_order_value),
                'total_stock_value': float(total_stock_value)
            }
        })

    @action(detail=False, methods=['get'])
    def recent_activities(self, request):
        """Get recent activities"""
        # Recent orders
        recent_orders = order.objects.select_related('customer').order_by('-date')[:5]
        recent_stock_orders = stock_order.objects.select_related('supplier').order_by('-date')[:5]

        orders_data = OrderSerializer(recent_orders, many=True).data
        stock_orders_data = StockOrderSerializer(recent_stock_orders, many=True).data

        return Response({
            'recent_orders': orders_data,
            'recent_stock_orders': stock_orders_data
        })
